"""Mock data for API demo mode."""

from datetime import datetime, timed<PERSON>ta
from typing import List
import random

from ebook_indexer.database.models import (
    BookDocument, FileInfo, BookMetadata, StructureInfo, ProcessingInfo,
    ProcessingStatus, Anomaly, AnomalyType, AnomalySeverity,
    ProcessingJob, JobStatus, JobStatistics, JobTiming,
    AnomalyReport
)


def create_mock_books(count: int = 50) -> List[BookDocument]:
    """Create mock book documents for demo purposes."""
    books = []
    
    # Sample data
    titles = [
        "Python Programming Fundamentals", "Advanced JavaScript Techniques", "Machine Learning Basics",
        "Web Development with React", "Database Design Principles", "Cybersecurity Essentials",
        "Data Science with Python", "Cloud Computing Guide", "Mobile App Development",
        "Artificial Intelligence Overview", "Software Engineering Practices", "Network Administration",
        "Digital Marketing Strategies", "Project Management Handbook", "UX/UI Design Principles"
    ]
    
    authors = [
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Lee", "<PERSON> White"
    ]
    
    publishers = [
        "Tech Publications", "Academic Press", "Digital Books Inc", "Learning Publishers",
        "Professional Guides", "Educational Resources", "Modern Publishing"
    ]
    
    collections = [
        "Programming-Books-2024", "Technical-References", "Educational-Materials",
        "Professional-Development", "Computer-Science-Library"
    ]
    
    formats = [".pdf", ".epub", ".mobi", ".txt"]
    
    for i in range(count):
        # Random selections
        title = random.choice(titles)
        author = random.choice(authors)
        publisher = random.choice(publishers)
        collection = random.choice(collections)
        file_format = random.choice(formats)
        
        # Create file info
        filename = f"{title.lower().replace(' ', '-')}{file_format}"
        file_path = f"/books/{collection}/{title.replace(' ', '-')}/{filename}"
        
        file_info = FileInfo(
            file_path=file_path,
            directory_path=f"/books/{collection}/{title.replace(' ', '-')}",
            filename=filename,
            file_extension=file_format,
            file_size=random.randint(1024*100, 1024*1024*50),  # 100KB to 50MB
            file_hash=f"sha256_{i:08d}",
            last_modified=datetime.utcnow() - timedelta(days=random.randint(1, 365))
        )
        
        # Create metadata
        metadata = BookMetadata(
            title=title,
            author=author,
            publisher=publisher,
            isbn=f"978-{random.randint(1000000000, 9999999999)}" if random.random() > 0.3 else None,
            pages=random.randint(50, 800) if random.random() > 0.2 else None,
            language="en",
            description=f"A comprehensive guide to {title.lower()}." if random.random() > 0.4 else None,
            publication_date=datetime.utcnow() - timedelta(days=random.randint(30, 3650))
        )
        
        # Create structure info
        structure_info = StructureInfo(
            expected_path=file_path,
            collection_name=collection,
            book_directory=title.replace(' ', '-'),
            nesting_level=random.randint(2, 4),
            follows_convention=random.random() > 0.2  # 80% follow convention
        )
        
        # Create processing info
        processing_info = ProcessingInfo(
            status=ProcessingStatus.COMPLETED if random.random() > 0.1 else ProcessingStatus.ERROR,
            retry_count=random.randint(0, 2),
            last_processed_date=datetime.utcnow() - timedelta(hours=random.randint(1, 72)),
            processing_time_ms=random.randint(100, 5000),
            job_id=f"job_{random.randint(1, 10):03d}"
        )
        
        # Create anomalies (some books have them)
        anomalies = []
        if random.random() < 0.3:  # 30% have anomalies
            anomaly_types = [AnomalyType.MISPLACED_FILE, AnomalyType.DEEP_NESTING, AnomalyType.NAMING_CONVENTION]
            anomaly_type = random.choice(anomaly_types)
            severity = random.choice([AnomalySeverity.LOW, AnomalySeverity.MEDIUM, AnomalySeverity.HIGH])
            
            anomaly = Anomaly(
                type=anomaly_type,
                severity=severity,
                description=f"Detected {anomaly_type.value} issue",
                detected_date=datetime.utcnow() - timedelta(hours=random.randint(1, 48)),
                resolved=random.random() > 0.6,  # 40% resolved
                suggested_action=f"Consider moving file to proper location"
            )
            anomalies.append(anomaly)
        
        # Create book document
        book = BookDocument(
            id=f"book_{i:08d}",
            file_info=file_info,
            metadata=metadata,
            structure_info=structure_info,
            processing=processing_info,
            anomalies=anomalies,
            created_date=datetime.utcnow() - timedelta(days=random.randint(1, 30))
        )
        
        books.append(book)
    
    return books


def create_mock_jobs(count: int = 10) -> List[ProcessingJob]:
    """Create mock processing jobs for demo purposes."""
    jobs = []
    
    for i in range(count):
        # Random job data
        total_files = random.randint(50, 500)
        processed_files = random.randint(int(total_files * 0.7), total_files)
        successful_files = random.randint(int(processed_files * 0.8), processed_files)
        failed_files = processed_files - successful_files
        anomalous_files = random.randint(0, int(successful_files * 0.3))
        
        statistics = JobStatistics(
            total_files=total_files,
            processed_files=processed_files,
            successful_files=successful_files,
            failed_files=failed_files,
            anomalous_files=anomalous_files,
            collections_found=random.randint(3, 8),
            processing_rate_files_per_sec=random.uniform(0.5, 3.0)
        )
        
        # Job timing
        start_time = datetime.utcnow() - timedelta(hours=random.randint(1, 168))  # Last week
        duration = random.randint(300, 7200)  # 5 minutes to 2 hours
        end_time = start_time + timedelta(seconds=duration)
        
        timing = JobTiming(
            start_time=start_time,
            end_time=end_time,
            duration_seconds=duration
        )
        
        # Job status
        statuses = [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.RUNNING]
        weights = [0.7, 0.2, 0.1]  # 70% completed, 20% failed, 10% running
        status = random.choices(statuses, weights=weights)[0]
        
        if status == JobStatus.RUNNING:
            timing.end_time = None
            timing.duration_seconds = None
        
        job = ProcessingJob(
            id=f"job_{i:03d}",
            root_directories=[f"/books/collection-{random.randint(1, 5)}"],
            status=status,
            statistics=statistics,
            timing=timing,
            created_date=start_time
        )
        
        jobs.append(job)
    
    return jobs


def create_mock_anomalies(count: int = 20) -> List[AnomalyReport]:
    """Create mock anomaly reports for demo purposes."""
    anomalies = []
    
    anomaly_descriptions = {
        AnomalyType.MISPLACED_FILE: "File found outside expected directory structure",
        AnomalyType.DEEP_NESTING: "File nested too deeply in directory structure",
        AnomalyType.NAMING_CONVENTION: "File or directory name doesn't follow naming convention",
        AnomalyType.MISSING_DIRECTORY: "Expected directory structure is missing",
        AnomalyType.DUPLICATE_FILE: "Duplicate file detected in different locations"
    }
    
    for i in range(count):
        anomaly_type = random.choice(list(AnomalyType))
        severity = random.choice(list(AnomalySeverity))
        
        anomaly = AnomalyReport(
            id=f"anomaly_{i:08d}",
            job_id=f"job_{random.randint(1, 10):03d}",
            file_path=f"/books/collection-{random.randint(1, 5)}/book-{random.randint(1, 100)}/file.pdf",
            anomaly_type=anomaly_type,
            severity=severity,
            description=anomaly_descriptions[anomaly_type],
            detected_date=datetime.utcnow() - timedelta(hours=random.randint(1, 168))
        )
        
        anomalies.append(anomaly)
    
    return anomalies


# Global mock data
_mock_books = None
_mock_jobs = None
_mock_anomalies = None


def get_mock_books() -> List[BookDocument]:
    """Get mock books, creating them if needed."""
    global _mock_books
    if _mock_books is None:
        _mock_books = create_mock_books(50)
    return _mock_books


def get_mock_jobs() -> List[ProcessingJob]:
    """Get mock jobs, creating them if needed."""
    global _mock_jobs
    if _mock_jobs is None:
        _mock_jobs = create_mock_jobs(10)
    return _mock_jobs


def get_mock_anomalies() -> List[AnomalyReport]:
    """Get mock anomalies, creating them if needed."""
    global _mock_anomalies
    if _mock_anomalies is None:
        _mock_anomalies = create_mock_anomalies(20)
    return _mock_anomalies
