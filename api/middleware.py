"""FastAPI middleware components."""

import time
import logging
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests and responses."""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process HTTP request and log details."""
        start_time = time.time()
        
        # Log request
        logger.info(
            f"Request: {request.method} {request.url.path} "
            f"from {request.client.host if request.client else 'unknown'}"
        )
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log response
            logger.info(
                f"Response: {response.status_code} "
                f"for {request.method} {request.url.path} "
                f"in {process_time:.3f}s"
            )
            
            # Add processing time header
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # Log error
            process_time = time.time() - start_time
            logger.error(
                f"Error: {str(e)} "
                f"for {request.method} {request.url.path} "
                f"in {process_time:.3f}s"
            )
            raise


class CacheControlMiddleware(BaseHTTPMiddleware):
    """Middleware for adding cache control headers."""

    def __init__(self, app, max_age: int = 300):
        super().__init__(app)
        self.max_age = max_age

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add cache control headers to responses."""
        response = await call_next(request)
        
        # Add cache control for API responses
        if request.url.path.startswith("/api/"):
            if request.method == "GET":
                response.headers["Cache-Control"] = f"public, max-age={self.max_age}"
            else:
                response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        
        return response
