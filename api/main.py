"""FastAPI application for Ebook Indexer Dashboard."""

from fastapi import Fast<PERSON>I, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import uvicorn
from contextlib import asynccontextmanager
import logging

from .routers import jobs, books, anomalies, operations, dashboard
from .middleware import LoggingMiddleware
from .dependencies import get_database_connection
from ebook_indexer.config.settings import get_config
from ebook_indexer.database.connection import init_connection
from ebook_indexer.utils.logging_config import setup_logging


# Global variables for application state
app_config = None
db_connection = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global app_config, db_connection
    
    # Startup
    try:
        # Load configuration
        app_config = get_config()
        
        # Setup logging
        setup_logging(app_config.logging)
        logger = logging.getLogger(__name__)
        logger.info("Starting Ebook Indexer API")
        
        # Initialize database connection
        db_connection = init_connection(
            mongodb_url=app_config.mongodb_url,
            database_name=app_config.database_name
        )
        
        # Test database connection
        health = db_connection.health_check()
        if not health['connected']:
            logger.error(f"Database connection failed: {health['error']}")
            raise Exception(f"Database connection failed: {health['error']}")
        
        logger.info("Database connection established")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise
    
    finally:
        # Shutdown
        logger.info("Shutting down Ebook Indexer API")
        if db_connection:
            db_connection.close()


# Create FastAPI application
app = FastAPI(
    title="Ebook Indexer API",
    description="REST API for Ebook Indexer Dashboard",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],  # React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "*.localhost"]
)

app.add_middleware(LoggingMiddleware)

# Include routers
app.include_router(
    dashboard.router,
    prefix="/api/v1/dashboard",
    tags=["dashboard"]
)

app.include_router(
    jobs.router,
    prefix="/api/v1/jobs",
    tags=["jobs"]
)

app.include_router(
    books.router,
    prefix="/api/v1/books",
    tags=["books"]
)

app.include_router(
    anomalies.router,
    prefix="/api/v1/anomalies",
    tags=["anomalies"]
)

app.include_router(
    operations.router,
    prefix="/api/v1/operations",
    tags=["operations"]
)


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Ebook Indexer API",
        "version": "1.0.0",
        "docs": "/api/docs"
    }


@app.get("/api/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check database connection
        health = db_connection.health_check()
        
        return {
            "status": "healthy" if health['connected'] else "unhealthy",
            "database": health,
            "version": "1.0.0"
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Health check failed: {e}")


def get_app_config():
    """Get application configuration."""
    return app_config


def get_db_connection():
    """Get database connection."""
    return db_connection


if __name__ == "__main__":
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
