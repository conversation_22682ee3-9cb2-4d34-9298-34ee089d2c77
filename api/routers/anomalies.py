"""Anomalies API router for anomaly management."""

from fastapi import APIRouter, Depends, HTTPException
from typing import Optional, List
import logging

from ..dependencies import (
    get_anomaly_repository,
    get_book_repository,
    get_pagination_params,
    get_anomaly_filters,
    PaginationParams,
    AnomalyFilters
)
from ..models.responses import (
    AnomalyResponse,
    AnomalyListResponse,
    AnomalySummaryStats,
    PaginationInfo,
    BaseResponse
)
from ..models.requests import AnomalyResolveRequest, BulkAnomalyResolveRequest
from ebook_indexer.database.repository import AnomalyRepository, BookRepository


router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("", response_model=AnomalyListResponse)
async def list_anomalies(
    pagination: PaginationParams = Depends(get_pagination_params),
    filters: AnomalyFilters = Depends(get_anomaly_filters),
    anomaly_repo: AnomalyRepository = Depends(get_anomaly_repository),
    book_repo: BookRepository = Depends(get_book_repository)
) -> AnomalyListResponse:
    """List anomalies with filtering and pagination."""
    try:
        # Get all anomalies for filtering
        all_anomalies = anomaly_repo.get_all_anomalies()
        
        # Apply filters
        filtered_anomalies = []
        for anomaly in all_anomalies:
            # Filter by job ID
            if filters.job_id:
                # Get the book to check job ID
                book = book_repo.get_book_by_id(anomaly.book_id)
                if not book or not book.processing or book.processing.job_id != filters.job_id:
                    continue
            
            # Filter by severity
            if filters.severity and anomaly.severity not in filters.severity:
                continue
            
            # Filter by anomaly types
            if filters.anomaly_types and anomaly.type not in filters.anomaly_types:
                continue
            
            # Filter by resolved status
            if filters.resolved is not None and anomaly.resolved != filters.resolved:
                continue
            
            # Filter by collection
            if filters.collection:
                book = book_repo.get_book_by_id(anomaly.book_id)
                if not book or not book.structure_info or book.structure_info.collection_name != filters.collection:
                    continue
            
            filtered_anomalies.append(anomaly)
        
        # Calculate pagination
        total = len(filtered_anomalies)
        pages = (total + pagination.per_page - 1) // pagination.per_page
        
        # Apply pagination
        start_idx = pagination.skip
        end_idx = start_idx + pagination.per_page
        paginated_anomalies = filtered_anomalies[start_idx:end_idx]
        
        # Convert to response models
        anomaly_responses = []
        for anomaly in paginated_anomalies:
            # Get book info for additional context
            book = book_repo.get_book_by_id(anomaly.book_id)
            file_path = book.file_info.file_path if book and book.file_info else "Unknown"
            collection_name = book.structure_info.collection_name if book and book.structure_info else None
            
            anomaly_responses.append(AnomalyResponse(
                id=str(anomaly.id),
                book_id=str(anomaly.book_id),
                file_path=file_path,
                anomaly_type=anomaly.type,
                severity=anomaly.severity,
                description=anomaly.description,
                suggested_action=anomaly.suggested_action,
                resolved=anomaly.resolved,
                detected_date=anomaly.detected_date,
                collection_name=collection_name
            ))
        
        # Generate summary statistics
        summary = _generate_anomaly_summary(all_anomalies)
        
        return AnomalyListResponse(
            anomalies=anomaly_responses,
            summary=summary,
            pagination=PaginationInfo(
                total=total,
                page=pagination.page,
                per_page=pagination.per_page,
                pages=pages
            )
        )
        
    except Exception as e:
        logger.error(f"Failed to list anomalies: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list anomalies: {e}")


@router.get("/{anomaly_id}", response_model=AnomalyResponse)
async def get_anomaly(
    anomaly_id: str,
    anomaly_repo: AnomalyRepository = Depends(get_anomaly_repository),
    book_repo: BookRepository = Depends(get_book_repository)
) -> AnomalyResponse:
    """Get a specific anomaly by ID."""
    try:
        anomaly = anomaly_repo.get_anomaly_by_id(anomaly_id)
        if not anomaly:
            raise HTTPException(status_code=404, detail="Anomaly not found")
        
        # Get book info for additional context
        book = book_repo.get_book_by_id(anomaly.book_id)
        file_path = book.file_info.file_path if book and book.file_info else "Unknown"
        collection_name = book.structure_info.collection_name if book and book.structure_info else None
        
        return AnomalyResponse(
            id=str(anomaly.id),
            book_id=str(anomaly.book_id),
            file_path=file_path,
            anomaly_type=anomaly.type,
            severity=anomaly.severity,
            description=anomaly.description,
            suggested_action=anomaly.suggested_action,
            resolved=anomaly.resolved,
            detected_date=anomaly.detected_date,
            collection_name=collection_name
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get anomaly {anomaly_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get anomaly: {e}")


@router.put("/{anomaly_id}/resolve", response_model=BaseResponse)
async def resolve_anomaly(
    anomaly_id: str,
    request: AnomalyResolveRequest,
    anomaly_repo: AnomalyRepository = Depends(get_anomaly_repository)
) -> BaseResponse:
    """Resolve or unresolve a specific anomaly."""
    try:
        anomaly = anomaly_repo.get_anomaly_by_id(anomaly_id)
        if not anomaly:
            raise HTTPException(status_code=404, detail="Anomaly not found")
        
        # Update anomaly resolution status
        anomaly.resolved = request.resolved
        if request.resolution_notes:
            # Add resolution notes to description or create a notes field
            if hasattr(anomaly, 'resolution_notes'):
                anomaly.resolution_notes = request.resolution_notes
            else:
                # Append to description for now
                anomaly.description += f" [Resolution: {request.resolution_notes}]"
        
        # Save updated anomaly
        anomaly_repo.save_anomaly(anomaly)
        
        action = "resolved" if request.resolved else "marked as unresolved"
        logger.info(f"Anomaly {anomaly_id} {action}")
        
        return BaseResponse(
            success=True,
            message=f"Anomaly {action} successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to resolve anomaly {anomaly_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to resolve anomaly: {e}")


@router.post("/resolve-bulk", response_model=BaseResponse)
async def resolve_anomalies_bulk(
    request: BulkAnomalyResolveRequest,
    anomaly_repo: AnomalyRepository = Depends(get_anomaly_repository)
) -> BaseResponse:
    """Resolve or unresolve multiple anomalies."""
    try:
        resolved_count = 0
        failed_count = 0
        
        for anomaly_id in request.anomaly_ids:
            try:
                anomaly = anomaly_repo.get_anomaly_by_id(anomaly_id)
                if not anomaly:
                    failed_count += 1
                    continue
                
                # Update anomaly resolution status
                anomaly.resolved = request.resolved
                if request.resolution_notes:
                    if hasattr(anomaly, 'resolution_notes'):
                        anomaly.resolution_notes = request.resolution_notes
                    else:
                        anomaly.description += f" [Resolution: {request.resolution_notes}]"
                
                # Save updated anomaly
                anomaly_repo.save_anomaly(anomaly)
                resolved_count += 1
                
            except Exception as e:
                logger.error(f"Failed to resolve anomaly {anomaly_id}: {e}")
                failed_count += 1
        
        action = "resolved" if request.resolved else "marked as unresolved"
        message = f"{resolved_count} anomalies {action}"
        if failed_count > 0:
            message += f", {failed_count} failed"
        
        logger.info(f"Bulk anomaly resolution: {message}")
        
        return BaseResponse(
            success=True,
            message=message
        )
        
    except Exception as e:
        logger.error(f"Failed to bulk resolve anomalies: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to bulk resolve anomalies: {e}")


@router.get("/summary/stats", response_model=AnomalySummaryStats)
async def get_anomaly_summary(
    anomaly_repo: AnomalyRepository = Depends(get_anomaly_repository)
) -> AnomalySummaryStats:
    """Get anomaly summary statistics."""
    try:
        all_anomalies = anomaly_repo.get_all_anomalies()
        return _generate_anomaly_summary(all_anomalies)
        
    except Exception as e:
        logger.error(f"Failed to get anomaly summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get anomaly summary: {e}")


# Helper functions
def _generate_anomaly_summary(anomalies) -> AnomalySummaryStats:
    """Generate summary statistics from anomalies list."""
    total_anomalies = len(anomalies)
    by_severity = {}
    by_type = {}
    resolved = 0
    unresolved = 0
    
    for anomaly in anomalies:
        # Count by severity
        severity = anomaly.severity
        by_severity[severity] = by_severity.get(severity, 0) + 1
        
        # Count by type
        anomaly_type = anomaly.type
        by_type[anomaly_type] = by_type.get(anomaly_type, 0) + 1
        
        # Count resolved/unresolved
        if anomaly.resolved:
            resolved += 1
        else:
            unresolved += 1
    
    return AnomalySummaryStats(
        total_anomalies=total_anomalies,
        by_severity=by_severity,
        by_type=by_type,
        resolved=resolved,
        unresolved=unresolved
    )
