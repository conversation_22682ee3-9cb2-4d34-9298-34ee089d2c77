"""Dashboard API router for app state visualization."""

from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
import logging
from datetime import datetime, timedelta

from ..dependencies import (
    get_database_connection,
    get_job_repository,
    get_book_repository,
    get_anomaly_repository,
    get_app_config
)
from ..models.responses import (
    DashboardOverview,
    DashboardSummary,
    RecentActivity,
    AnomalySummaryStats,
    ProcessingStats,
    BaseResponse,
    AppState,
    SystemHealth,
    JobResponse,
    OperationStatus
)
from ebook_indexer.database.repository import JobRepository, BookRepository, AnomalyRepository
from ebook_indexer.config.settings import AppConfig


router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/overview", response_model=DashboardOverview)
async def get_dashboard_overview(
    job_repo: JobRepository = Depends(get_job_repository),
    book_repo: BookRepository = Depends(get_book_repository),
    anomaly_repo: AnomalyRepository = Depends(get_anomaly_repository),
    config: AppConfig = Depends(get_app_config)
) -> DashboardOverview:
    """Get complete dashboard overview with all app state information."""
    try:
        # Get summary statistics
        summary = await _get_dashboard_summary(job_repo, book_repo, anomaly_repo)

        # Get recent activity
        recent_activity = await _get_recent_activity(job_repo)

        # Get anomaly summary
        anomaly_summary = await _get_anomaly_summary(anomaly_repo)

        # Get processing statistics
        processing_stats = await _get_processing_stats(job_repo, book_repo)

        return DashboardOverview(
            summary=summary,
            recent_activity=recent_activity,
            anomaly_summary=anomaly_summary,
            processing_stats=processing_stats
        )

    except Exception as e:
        logger.error(f"Failed to get dashboard overview: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard overview: {e}")


@router.get("/summary", response_model=DashboardSummary)
async def get_dashboard_summary(
    job_repo: JobRepository = Depends(get_job_repository),
    book_repo: BookRepository = Depends(get_book_repository),
    anomaly_repo: AnomalyRepository = Depends(get_anomaly_repository)
) -> DashboardSummary:
    """Get dashboard summary statistics."""
    try:
        return await _get_dashboard_summary(job_repo, book_repo, anomaly_repo)
    except Exception as e:
        logger.error(f"Failed to get dashboard summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard summary: {e}")


@router.get("/recent-activity", response_model=list[RecentActivity])
async def get_recent_activity(
    limit: int = 10,
    job_repo: JobRepository = Depends(get_job_repository)
) -> list[RecentActivity]:
    """Get recent job activity."""
    try:
        return await _get_recent_activity(job_repo, limit)
    except Exception as e:
        logger.error(f"Failed to get recent activity: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get recent activity: {e}")


@router.get("/processing-stats", response_model=ProcessingStats)
async def get_processing_stats(
    job_repo: JobRepository = Depends(get_job_repository),
    book_repo: BookRepository = Depends(get_book_repository)
) -> ProcessingStats:
    """Get processing performance statistics."""
    try:
        return await _get_processing_stats(job_repo, book_repo)
    except Exception as e:
        logger.error(f"Failed to get processing stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get processing stats: {e}")


@router.get("/system-status")
async def get_system_status(
    db_connection = Depends(get_database_connection),
    config: AppConfig = Depends(get_app_config)
) -> Dict[str, Any]:
    """Get system status and health information."""
    try:
        # Check database health
        db_health = db_connection.health_check()

        # Get configuration status
        config_status = {
            "mongodb_url": config.mongodb_url,
            "database_name": config.database_name,
            "max_workers": config.max_workers,
            "batch_size": config.batch_size,
            "supported_extensions": config.supported_extensions,
            "root_directories": config.root_directories
        }

        return {
            "status": "healthy" if db_health['connected'] else "unhealthy",
            "timestamp": datetime.utcnow(),
            "database": db_health,
            "configuration": config_status,
            "version": "1.0.0"
        }

    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get system status: {e}")


@router.get("/app-state", response_model=AppState)
async def get_complete_app_state(
    job_repo: JobRepository = Depends(get_job_repository),
    book_repo: BookRepository = Depends(get_book_repository),
    anomaly_repo: AnomalyRepository = Depends(get_anomaly_repository),
    db_connection = Depends(get_database_connection),
    config: AppConfig = Depends(get_app_config)
) -> AppState:
    """Get complete application state for React dashboard."""
    try:
        # Get system health
        db_health = db_connection.health_check()
        config_status = {
            "mongodb_url": config.mongodb_url,
            "database_name": config.database_name,
            "max_workers": config.max_workers,
            "batch_size": config.batch_size,
            "supported_extensions": config.supported_extensions,
            "root_directories": config.root_directories
        }

        system_health = SystemHealth(
            status="healthy" if db_health['connected'] else "unhealthy",
            database=db_health,
            configuration=config_status,
            version="1.0.0",
            timestamp=datetime.utcnow()
        )

        # Get dashboard summary
        dashboard_summary = await _get_dashboard_summary(job_repo, book_repo, anomaly_repo)

        # Get recent jobs (convert to JobResponse format)
        recent_jobs_data = job_repo.get_recent_jobs(limit=5)
        recent_jobs = []
        for job in recent_jobs_data:
            from ..routers.jobs import _convert_job_to_response
            recent_jobs.append(_convert_job_to_response(job))

        # Get active operations (mock for now - in production, get from operations store)
        active_operations = []  # Would be populated from operations tracking

        return AppState(
            system_health=system_health,
            dashboard_summary=dashboard_summary,
            active_operations=active_operations,
            recent_jobs=recent_jobs
        )

    except Exception as e:
        logger.error(f"Failed to get complete app state: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get complete app state: {e}")


# Helper functions
async def _get_dashboard_summary(
    job_repo: JobRepository,
    book_repo: BookRepository,
    anomaly_repo: AnomalyRepository
) -> DashboardSummary:
    """Get dashboard summary statistics."""
    # Get total counts
    total_books = len(book_repo.get_all_books())
    total_anomalies = len(anomaly_repo.get_all_anomalies())

    # Get active jobs count
    recent_jobs = job_repo.get_recent_jobs(limit=50)
    active_jobs = len([job for job in recent_jobs if job.status.value in ['pending', 'running']])

    # Get collections count (unique collection names)
    books = book_repo.get_all_books()
    collections = set()
    last_scan_date = None

    for book in books:
        if book.structure_info and book.structure_info.collection_name:
            collections.add(book.structure_info.collection_name)
        if book.processing and book.processing.last_processed_date:
            if not last_scan_date or book.processing.last_processed_date > last_scan_date:
                last_scan_date = book.processing.last_processed_date

    return DashboardSummary(
        total_books=total_books,
        total_collections=len(collections),
        total_anomalies=total_anomalies,
        active_jobs=active_jobs,
        last_scan_date=last_scan_date
    )


async def _get_recent_activity(job_repo: JobRepository, limit: int = 10) -> list[RecentActivity]:
    """Get recent job activity."""
    recent_jobs = job_repo.get_recent_jobs(limit=limit)

    activity = []
    for job in recent_jobs:
        duration = "Unknown"
        if job.timing and job.timing.duration_seconds:
            duration = f"{job.timing.duration_seconds}s"

        activity.append(RecentActivity(
            job_id=str(job.id),
            status=job.status.value,
            files_processed=job.statistics.processed_files if job.statistics else 0,
            anomalies_found=job.statistics.anomalous_files if job.statistics else 0,
            duration=duration,
            created_date=job.created_date
        ))

    return activity


async def _get_anomaly_summary(anomaly_repo: AnomalyRepository) -> AnomalySummaryStats:
    """Get anomaly summary statistics."""
    anomalies = anomaly_repo.get_all_anomalies()

    total_anomalies = len(anomalies)
    by_severity = {}
    by_type = {}
    resolved = 0
    unresolved = 0

    for anomaly in anomalies:
        # Count by severity
        severity = anomaly.severity
        by_severity[severity] = by_severity.get(severity, 0) + 1

        # Count by type
        anomaly_type = anomaly.type
        by_type[anomaly_type] = by_type.get(anomaly_type, 0) + 1

        # Count resolved/unresolved
        if anomaly.resolved:
            resolved += 1
        else:
            unresolved += 1

    return AnomalySummaryStats(
        total_anomalies=total_anomalies,
        by_severity=by_severity,
        by_type=by_type,
        resolved=resolved,
        unresolved=unresolved
    )


async def _get_processing_stats(job_repo: JobRepository, book_repo: BookRepository) -> ProcessingStats:
    """Get processing performance statistics."""
    recent_jobs = job_repo.get_recent_jobs(limit=10)

    if not recent_jobs:
        return ProcessingStats(
            files_per_hour=0.0,
            success_rate=0.0,
            avg_processing_time_ms=0.0
        )

    total_files = 0
    successful_files = 0
    total_processing_time = 0
    total_duration_hours = 0

    for job in recent_jobs:
        if job.statistics:
            total_files += job.statistics.processed_files
            successful_files += job.statistics.successful_files

        if job.timing and job.timing.duration_seconds:
            total_duration_hours += job.timing.duration_seconds / 3600

    # Calculate averages
    files_per_hour = total_files / total_duration_hours if total_duration_hours > 0 else 0
    success_rate = (successful_files / total_files * 100) if total_files > 0 else 0

    # Get average processing time from books
    books = book_repo.get_all_books()
    processing_times = [
        book.processing.processing_time_ms
        for book in books
        if book.processing and book.processing.processing_time_ms
    ]
    avg_processing_time_ms = sum(processing_times) / len(processing_times) if processing_times else 0

    return ProcessingStats(
        files_per_hour=round(files_per_hour, 2),
        success_rate=round(success_rate, 2),
        avg_processing_time_ms=round(avg_processing_time_ms, 2)
    )
