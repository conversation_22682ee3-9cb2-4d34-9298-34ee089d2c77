"""Books API router for book data and search."""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional, List, Dict, Any
import logging

from ..dependencies import (
    get_book_repository,
    get_pagination_params,
    get_book_filters,
    PaginationParams,
    BookFilters
)
from ..models.responses import (
    BookResponse,
    BookListResponse,
    CollectionListResponse,
    CollectionInfo,
    FileInfo,
    BookMetadata,
    StructureInfo,
    ProcessingInfo,
    AnomalyInfo,
    PaginationInfo
)
from ..models.requests import SearchRequest
from ebook_indexer.database.repository import BookRepository


router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("", response_model=BookListResponse)
async def list_books(
    pagination: PaginationParams = Depends(get_pagination_params),
    filters: BookFilters = Depends(get_book_filters),
    book_repo: BookRepository = Depends(get_book_repository)
) -> BookListResponse:
    """List books with filtering and pagination."""
    try:
        # Get all books for filtering
        all_books = book_repo.get_all_books()
        
        # Apply filters
        filtered_books = []
        for book in all_books:
            # Filter by job ID
            if filters.job_id and (not book.processing or book.processing.job_id != filters.job_id):
                continue
            
            # Filter by anomalies
            if filters.has_anomalies is not None:
                has_anomalies = len(book.anomalies) > 0
                if has_anomalies != filters.has_anomalies:
                    continue
            
            # Filter by status
            if filters.status and (not book.processing or book.processing.status != filters.status):
                continue
            
            # Filter by collection
            if filters.collection and (not book.structure_info or book.structure_info.collection_name != filters.collection):
                continue
            
            # Filter by format
            if filters.format and (not book.metadata or book.metadata.format != filters.format):
                continue
            
            # Search filter
            if filters.search:
                search_text = filters.search.lower()
                searchable_fields = [
                    book.file_info.filename.lower() if book.file_info else "",
                    book.metadata.title.lower() if book.metadata and book.metadata.title else "",
                    book.metadata.author.lower() if book.metadata and book.metadata.author else "",
                    book.metadata.publisher.lower() if book.metadata and book.metadata.publisher else ""
                ]
                
                if not any(search_text in field for field in searchable_fields):
                    continue
            
            filtered_books.append(book)
        
        # Calculate pagination
        total = len(filtered_books)
        pages = (total + pagination.per_page - 1) // pagination.per_page
        
        # Apply pagination
        start_idx = pagination.skip
        end_idx = start_idx + pagination.per_page
        paginated_books = filtered_books[start_idx:end_idx]
        
        # Convert to response models
        book_responses = []
        for book in paginated_books:
            book_responses.append(_convert_book_to_response(book))
        
        # Create filters applied summary
        filters_applied = {
            "job_id": filters.job_id,
            "has_anomalies": filters.has_anomalies,
            "status": filters.status,
            "collection": filters.collection,
            "format": filters.format,
            "search": filters.search
        }
        
        return BookListResponse(
            books=book_responses,
            pagination=PaginationInfo(
                total=total,
                page=pagination.page,
                per_page=pagination.per_page,
                pages=pages
            ),
            filters_applied=filters_applied
        )
        
    except Exception as e:
        logger.error(f"Failed to list books: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list books: {e}")


@router.get("/{book_id}", response_model=BookResponse)
async def get_book(
    book_id: str,
    book_repo: BookRepository = Depends(get_book_repository)
) -> BookResponse:
    """Get a specific book by ID."""
    try:
        book = book_repo.get_book_by_id(book_id)
        if not book:
            raise HTTPException(status_code=404, detail="Book not found")
        
        return _convert_book_to_response(book)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get book {book_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get book: {e}")


@router.post("/search", response_model=BookListResponse)
async def search_books(
    request: SearchRequest,
    pagination: PaginationParams = Depends(get_pagination_params),
    book_repo: BookRepository = Depends(get_book_repository)
) -> BookListResponse:
    """Search books with advanced filtering."""
    try:
        # Get all books
        all_books = book_repo.get_all_books()
        
        # Perform search
        search_results = []
        search_query = request.query.lower()
        
        for book in all_books:
            # Determine which fields to search
            search_fields = request.fields or ['title', 'author', 'publisher', 'description', 'filename']
            
            # Build searchable text
            searchable_text = []
            
            if 'filename' in search_fields and book.file_info:
                searchable_text.append(book.file_info.filename.lower())
            
            if book.metadata:
                if 'title' in search_fields and book.metadata.title:
                    searchable_text.append(book.metadata.title.lower())
                if 'author' in search_fields and book.metadata.author:
                    searchable_text.append(book.metadata.author.lower())
                if 'publisher' in search_fields and book.metadata.publisher:
                    searchable_text.append(book.metadata.publisher.lower())
                if 'description' in search_fields and book.metadata.description:
                    searchable_text.append(book.metadata.description.lower())
                if 'isbn' in search_fields and book.metadata.isbn:
                    searchable_text.append(book.metadata.isbn.lower())
            
            # Check if query matches any searchable text
            combined_text = " ".join(searchable_text)
            if search_query in combined_text:
                # Apply additional filters if provided
                if request.filters:
                    # Apply format filter
                    if 'format' in request.filters:
                        if not book.metadata or book.metadata.format != request.filters['format']:
                            continue
                    
                    # Apply has_anomalies filter
                    if 'has_anomalies' in request.filters:
                        has_anomalies = len(book.anomalies) > 0
                        if has_anomalies != request.filters['has_anomalies']:
                            continue
                
                search_results.append(book)
        
        # Calculate pagination
        total = len(search_results)
        pages = (total + pagination.per_page - 1) // pagination.per_page
        
        # Apply pagination
        start_idx = pagination.skip
        end_idx = start_idx + pagination.per_page
        paginated_results = search_results[start_idx:end_idx]
        
        # Convert to response models
        book_responses = []
        for book in paginated_results:
            book_responses.append(_convert_book_to_response(book))
        
        return BookListResponse(
            books=book_responses,
            pagination=PaginationInfo(
                total=total,
                page=pagination.page,
                per_page=pagination.per_page,
                pages=pages
            ),
            filters_applied={
                "search_query": request.query,
                "search_fields": request.fields,
                "additional_filters": request.filters
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to search books: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to search books: {e}")


@router.get("/collections/list", response_model=CollectionListResponse)
async def list_collections(
    book_repo: BookRepository = Depends(get_book_repository)
) -> CollectionListResponse:
    """List all collections with statistics."""
    try:
        # Get all books
        all_books = book_repo.get_all_books()
        
        # Group by collection
        collections_data = {}
        
        for book in all_books:
            collection_name = "Unknown"
            if book.structure_info and book.structure_info.collection_name:
                collection_name = book.structure_info.collection_name
            
            if collection_name not in collections_data:
                collections_data[collection_name] = {
                    "books": [],
                    "total_size": 0,
                    "anomaly_count": 0,
                    "last_updated": None
                }
            
            collections_data[collection_name]["books"].append(book)
            
            # Add file size
            if book.file_info:
                collections_data[collection_name]["total_size"] += book.file_info.file_size
            
            # Count anomalies
            collections_data[collection_name]["anomaly_count"] += len(book.anomalies)
            
            # Track last updated
            if book.processing and book.processing.last_processed_date:
                current_last = collections_data[collection_name]["last_updated"]
                if not current_last or book.processing.last_processed_date > current_last:
                    collections_data[collection_name]["last_updated"] = book.processing.last_processed_date
        
        # Convert to response models
        collections = []
        for name, data in collections_data.items():
            collections.append(CollectionInfo(
                name=name,
                book_count=len(data["books"]),
                total_size_bytes=data["total_size"],
                anomaly_count=data["anomaly_count"],
                last_updated=data["last_updated"] or all_books[0].created_date if all_books else None
            ))
        
        # Sort by book count (descending)
        collections.sort(key=lambda x: x.book_count, reverse=True)
        
        return CollectionListResponse(collections=collections)
        
    except Exception as e:
        logger.error(f"Failed to list collections: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list collections: {e}")


# Helper functions
def _convert_book_to_response(book) -> BookResponse:
    """Convert database book model to API response model."""
    return BookResponse(
        id=str(book.id),
        file_info=FileInfo(
            file_path=book.file_info.file_path,
            filename=book.file_info.filename,
            file_size=book.file_info.file_size,
            last_modified=book.file_info.last_modified
        ) if book.file_info else None,
        metadata=BookMetadata(
            title=book.metadata.title if book.metadata else None,
            author=book.metadata.author if book.metadata else None,
            format=book.metadata.format if book.metadata else None,
            pages=book.metadata.pages if book.metadata else None,
            publisher=book.metadata.publisher if book.metadata else None,
            isbn=book.metadata.isbn if book.metadata else None,
            language=book.metadata.language if book.metadata else None,
            description=book.metadata.description if book.metadata else None
        ) if book.metadata else BookMetadata(),
        structure_info=StructureInfo(
            collection_name=book.structure_info.collection_name if book.structure_info else None,
            book_directory=book.structure_info.book_directory if book.structure_info else None,
            nesting_level=book.structure_info.nesting_level if book.structure_info else 0,
            follows_convention=book.structure_info.follows_convention if book.structure_info else False
        ) if book.structure_info else StructureInfo(nesting_level=0, follows_convention=False),
        processing=ProcessingInfo(
            status=book.processing.status.value if book.processing else "unknown",
            job_id=book.processing.job_id if book.processing else None,
            processing_time_ms=book.processing.processing_time_ms if book.processing else None,
            retry_count=book.processing.retry_count if book.processing else 0,
            last_processed_date=book.processing.last_processed_date if book.processing else None
        ) if book.processing else ProcessingInfo(status="unknown"),
        anomalies=[
            AnomalyInfo(
                type=anomaly.type,
                severity=anomaly.severity,
                description=anomaly.description,
                suggested_action=anomaly.suggested_action,
                resolved=anomaly.resolved,
                detected_date=anomaly.detected_date
            )
            for anomaly in book.anomalies
        ],
        created_date=book.created_date
    )
