"""Jobs API router for job management and monitoring."""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional, List
import logging

from ..dependencies import (
    get_job_repository, 
    get_indexer,
    get_pagination_params,
    get_job_filters,
    PaginationParams,
    JobFilters
)
from ..models.responses import (
    JobResponse,
    JobListResponse,
    JobStatistics,
    JobTiming,
    PaginationInfo,
    BaseResponse
)
from ..models.requests import JobCreateRequest
from ebook_indexer.database.repository import JobRepository
from ebook_indexer.core.indexer import EbookIndexer


router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("", response_model=JobListResponse)
async def list_jobs(
    pagination: PaginationParams = Depends(get_pagination_params),
    filters: JobFilters = Depends(get_job_filters),
    job_repo: JobRepository = Depends(get_job_repository)
) -> JobListResponse:
    """List jobs with filtering and pagination."""
    try:
        # Get jobs with filters
        all_jobs = job_repo.get_recent_jobs(limit=1000)  # Get more for filtering
        
        # Apply filters
        filtered_jobs = []
        for job in all_jobs:
            # Filter by status
            if filters.status and job.status.value != filters.status:
                continue
            
            # Filter by root directory
            if filters.root_directory:
                if not any(filters.root_directory in root_dir for root_dir in job.root_directories):
                    continue
            
            filtered_jobs.append(job)
        
        # Calculate pagination
        total = len(filtered_jobs)
        pages = (total + pagination.per_page - 1) // pagination.per_page
        
        # Apply pagination
        start_idx = pagination.skip
        end_idx = start_idx + pagination.per_page
        paginated_jobs = filtered_jobs[start_idx:end_idx]
        
        # Convert to response models
        job_responses = []
        for job in paginated_jobs:
            job_responses.append(_convert_job_to_response(job))
        
        return JobListResponse(
            jobs=job_responses,
            pagination=PaginationInfo(
                total=total,
                page=pagination.page,
                per_page=pagination.per_page,
                pages=pages
            )
        )
        
    except Exception as e:
        logger.error(f"Failed to list jobs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list jobs: {e}")


@router.get("/{job_id}", response_model=JobResponse)
async def get_job(
    job_id: str,
    job_repo: JobRepository = Depends(get_job_repository)
) -> JobResponse:
    """Get a specific job by ID."""
    try:
        job = job_repo.get_job_by_id(job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return _convert_job_to_response(job)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job {job_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get job: {e}")


@router.get("/{job_id}/status")
async def get_job_status(
    job_id: str,
    job_repo: JobRepository = Depends(get_job_repository)
) -> dict:
    """Get real-time job status."""
    try:
        job = job_repo.get_job_by_id(job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        # Calculate progress percentage
        progress_percentage = 0.0
        if job.statistics and job.statistics.total_files > 0:
            progress_percentage = (job.statistics.processed_files / job.statistics.total_files) * 100
        
        return {
            "job_id": job_id,
            "status": job.status.value,
            "progress": {
                "processed": job.statistics.processed_files if job.statistics else 0,
                "total": job.statistics.total_files if job.statistics else 0,
                "percentage": round(progress_percentage, 2),
                "successful": job.statistics.successful_files if job.statistics else 0,
                "failed": job.statistics.failed_files if job.statistics else 0,
                "anomalous": job.statistics.anomalous_files if job.statistics else 0
            },
            "timing": {
                "start_time": job.timing.start_time if job.timing else None,
                "end_time": job.timing.end_time if job.timing else None,
                "duration_seconds": job.timing.duration_seconds if job.timing else None,
                "estimated_completion": job.timing.estimated_completion if job.timing else None
            },
            "last_updated": job.updated_date if hasattr(job, 'updated_date') else job.created_date
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job status {job_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get job status: {e}")


@router.post("", response_model=JobResponse)
async def create_job(
    request: JobCreateRequest,
    indexer: EbookIndexer = Depends(get_indexer),
    job_repo: JobRepository = Depends(get_job_repository)
) -> JobResponse:
    """Create a new indexing job."""
    try:
        # Note: This creates the job but doesn't start it immediately
        # Use the operations endpoint to actually start indexing
        
        # Validate directories exist (basic check)
        import os
        for directory in request.root_directories:
            if not os.path.exists(directory):
                raise HTTPException(
                    status_code=400, 
                    detail=f"Directory does not exist: {directory}"
                )
        
        # Create job record
        from ebook_indexer.database.models import ProcessingJob, JobStatus
        from datetime import datetime
        
        job = ProcessingJob(
            root_directories=request.root_directories,
            status=JobStatus.PENDING,
            created_date=datetime.utcnow()
        )
        
        # Save job
        job_id = job_repo.save_job(job)
        job.id = job_id
        
        logger.info(f"Created job {job_id} for directories {request.root_directories}")
        
        return _convert_job_to_response(job)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create job: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create job: {e}")


@router.delete("/{job_id}")
async def cancel_job(
    job_id: str,
    job_repo: JobRepository = Depends(get_job_repository)
) -> BaseResponse:
    """Cancel a job (if it's still pending or running)."""
    try:
        job = job_repo.get_job_by_id(job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        if job.status.value not in ['pending', 'running']:
            raise HTTPException(
                status_code=400, 
                detail=f"Cannot cancel job with status: {job.status.value}"
            )
        
        # Update job status to cancelled
        from ebook_indexer.database.models import JobStatus
        job.status = JobStatus.CANCELLED
        job_repo.save_job(job)
        
        logger.info(f"Cancelled job {job_id}")
        
        return BaseResponse(
            success=True,
            message=f"Job {job_id} cancelled"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel job {job_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cancel job: {e}")


@router.get("/{job_id}/summary")
async def get_job_summary(
    job_id: str,
    indexer: EbookIndexer = Depends(get_indexer)
) -> dict:
    """Get detailed job summary including books and anomalies."""
    try:
        summary = indexer.get_indexing_summary(job_id)
        return summary
        
    except Exception as e:
        logger.error(f"Failed to get job summary {job_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get job summary: {e}")


# Helper functions
def _convert_job_to_response(job) -> JobResponse:
    """Convert database job model to API response model."""
    return JobResponse(
        id=str(job.id),
        status=job.status.value,
        root_directories=job.root_directories,
        statistics=JobStatistics(
            total_files=job.statistics.total_files if job.statistics else 0,
            processed_files=job.statistics.processed_files if job.statistics else 0,
            successful_files=job.statistics.successful_files if job.statistics else 0,
            failed_files=job.statistics.failed_files if job.statistics else 0,
            anomalous_files=job.statistics.anomalous_files if job.statistics else 0,
            collections_found=job.statistics.collections_found if job.statistics else 0,
            processing_rate_files_per_sec=job.statistics.processing_rate_files_per_sec if job.statistics else 0.0
        ),
        timing=JobTiming(
            start_time=job.timing.start_time if job.timing else None,
            end_time=job.timing.end_time if job.timing else None,
            duration_seconds=job.timing.duration_seconds if job.timing else None,
            estimated_completion=job.timing.estimated_completion if job.timing else None
        ),
        created_date=job.created_date
    )
