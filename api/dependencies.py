"""FastAPI dependencies."""

from fastapi import Depends, HTTPException, status
from typing import Optional
import logging

from ebook_indexer.database.connection import get_connection
from ebook_indexer.database.repository import BookRepository, JobRepository, AnomalyRepository
from ebook_indexer.core.indexer import EbookIndexer
from ebook_indexer.config.settings import AppConfig


logger = logging.getLogger(__name__)


def get_database_connection():
    """Get database connection dependency."""
    try:
        connection = get_connection()
        health = connection.health_check()
        if not health['connected']:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"Database not available: {health['error']}"
            )
        return connection
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database connection failed"
        )


def get_book_repository(db=Depends(get_database_connection)) -> BookRepository:
    """Get book repository dependency."""
    return BookRepository()


def get_job_repository(db=Depends(get_database_connection)) -> JobRepository:
    """Get job repository dependency."""
    return JobRepository()


def get_anomaly_repository(db=Depends(get_database_connection)) -> AnomalyRepository:
    """Get anomaly repository dependency."""
    return AnomalyRepository()


def get_app_config() -> AppConfig:
    """Get application configuration dependency."""
    from .main import get_app_config
    config = get_app_config()
    if not config:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Application configuration not available"
        )
    return config


def get_indexer(config: AppConfig = Depends(get_app_config)) -> EbookIndexer:
    """Get ebook indexer dependency."""
    try:
        return EbookIndexer(config)
    except Exception as e:
        logger.error(f"Failed to create indexer: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Indexer service not available"
        )


# Pagination dependency
class PaginationParams:
    """Pagination parameters."""
    
    def __init__(self, page: int = 1, per_page: int = 20):
        if page < 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Page must be >= 1"
            )
        if per_page < 1 or per_page > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Per page must be between 1 and 100"
            )
        
        self.page = page
        self.per_page = per_page
        self.skip = (page - 1) * per_page


def get_pagination_params(page: int = 1, per_page: int = 20) -> PaginationParams:
    """Get pagination parameters dependency."""
    return PaginationParams(page, per_page)


# Filter dependencies
class BookFilters:
    """Book filtering parameters."""
    
    def __init__(
        self,
        job_id: Optional[str] = None,
        has_anomalies: Optional[bool] = None,
        status: Optional[str] = None,
        collection: Optional[str] = None,
        format: Optional[str] = None,
        search: Optional[str] = None
    ):
        self.job_id = job_id
        self.has_anomalies = has_anomalies
        self.status = status
        self.collection = collection
        self.format = format
        self.search = search


def get_book_filters(
    job_id: Optional[str] = None,
    has_anomalies: Optional[bool] = None,
    status: Optional[str] = None,
    collection: Optional[str] = None,
    format: Optional[str] = None,
    search: Optional[str] = None
) -> BookFilters:
    """Get book filtering parameters dependency."""
    return BookFilters(job_id, has_anomalies, status, collection, format, search)


class AnomalyFilters:
    """Anomaly filtering parameters."""
    
    def __init__(
        self,
        job_id: Optional[str] = None,
        severity: Optional[list] = None,
        anomaly_types: Optional[list] = None,
        resolved: Optional[bool] = None,
        collection: Optional[str] = None
    ):
        self.job_id = job_id
        self.severity = severity or []
        self.anomaly_types = anomaly_types or []
        self.resolved = resolved
        self.collection = collection


def get_anomaly_filters(
    job_id: Optional[str] = None,
    severity: Optional[str] = None,
    types: Optional[str] = None,
    resolved: Optional[bool] = None,
    collection: Optional[str] = None
) -> AnomalyFilters:
    """Get anomaly filtering parameters dependency."""
    # Parse comma-separated values
    severity_list = severity.split(',') if severity else []
    types_list = types.split(',') if types else []
    
    return AnomalyFilters(job_id, severity_list, types_list, resolved, collection)


class JobFilters:
    """Job filtering parameters."""
    
    def __init__(
        self,
        status: Optional[str] = None,
        root_directory: Optional[str] = None
    ):
        self.status = status
        self.root_directory = root_directory


def get_job_filters(
    status: Optional[str] = None,
    root_directory: Optional[str] = None
) -> JobFilters:
    """Get job filtering parameters dependency."""
    return JobFilters(status, root_directory)
