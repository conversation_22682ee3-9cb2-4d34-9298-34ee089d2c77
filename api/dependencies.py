"""FastAPI dependencies."""

from fastapi import Depends, HTTPException, status
from typing import Optional
import logging

from ebook_indexer.database.connection import get_connection
from ebook_indexer.database.repository import BookRepository, JobRepository, AnomalyRepository
from ebook_indexer.core.indexer import EbookIndexer
from ebook_indexer.config.settings import AppConfig
from .mock_data import get_mock_books, get_mock_jobs, get_mock_anomalies


logger = logging.getLogger(__name__)


class MockBookRepository:
    """Mock book repository for demo mode."""

    def get_all_books(self):
        return get_mock_books()

    def get_book_by_id(self, book_id: str):
        books = get_mock_books()
        for book in books:
            if str(book.id) == book_id:
                return book
        return None


class MockJobRepository:
    """Mock job repository for demo mode."""

    def get_recent_jobs(self, limit: int = 10):
        jobs = get_mock_jobs()
        return sorted(jobs, key=lambda x: x.created_date, reverse=True)[:limit]

    def get_job_by_id(self, job_id: str):
        jobs = get_mock_jobs()
        for job in jobs:
            if str(job.id) == job_id:
                return job
        return None

    def get_retry_candidates(self, job_id: str = None):
        # Return some mock retry candidates
        return [f"file_{i}.pdf" for i in range(5)]

    def save_job(self, job):
        return str(job.id) if hasattr(job, 'id') else "mock_job_id"


class MockAnomalyRepository:
    """Mock anomaly repository for demo mode."""

    def get_all_anomalies(self):
        return get_mock_anomalies()

    def get_anomaly_by_id(self, anomaly_id: str):
        anomalies = get_mock_anomalies()
        for anomaly in anomalies:
            if str(anomaly.id) == anomaly_id:
                return anomaly
        return None

    def save_anomaly(self, anomaly):
        return str(anomaly.id) if hasattr(anomaly, 'id') else "mock_anomaly_id"


def get_database_connection():
    """Get database connection dependency."""
    try:
        connection = get_connection()
        if connection is None:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Database not available - running in limited mode"
            )
        health = connection.health_check()
        if not health['connected']:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"Database not available: {health['error']}"
            )
        return connection
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database connection failed - running in limited mode"
        )


def get_book_repository() -> BookRepository:
    """Get book repository dependency."""
    try:
        # Test database connection first
        get_database_connection()
        return BookRepository()
    except HTTPException:
        # Return mock repository in demo mode
        return MockBookRepository()


def get_job_repository() -> JobRepository:
    """Get job repository dependency."""
    try:
        # Test database connection first
        get_database_connection()
        return JobRepository()
    except HTTPException:
        # Return mock repository in demo mode
        return MockJobRepository()


def get_anomaly_repository() -> AnomalyRepository:
    """Get anomaly repository dependency."""
    try:
        # Test database connection first
        get_database_connection()
        return AnomalyRepository()
    except HTTPException:
        # Return mock repository in demo mode
        return MockAnomalyRepository()


def get_app_config() -> AppConfig:
    """Get application configuration dependency."""
    from .main import get_app_config
    config = get_app_config()
    if not config:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Application configuration not available"
        )
    return config


def get_indexer(config: AppConfig = Depends(get_app_config)) -> EbookIndexer:
    """Get ebook indexer dependency."""
    try:
        return EbookIndexer(config)
    except Exception as e:
        logger.error(f"Failed to create indexer: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Indexer service not available"
        )


# Pagination dependency
class PaginationParams:
    """Pagination parameters."""

    def __init__(self, page: int = 1, per_page: int = 20):
        if page < 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Page must be >= 1"
            )
        if per_page < 1 or per_page > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Per page must be between 1 and 100"
            )

        self.page = page
        self.per_page = per_page
        self.skip = (page - 1) * per_page


def get_pagination_params(page: int = 1, per_page: int = 20) -> PaginationParams:
    """Get pagination parameters dependency."""
    return PaginationParams(page, per_page)


# Filter dependencies
class BookFilters:
    """Book filtering parameters."""

    def __init__(
        self,
        job_id: Optional[str] = None,
        has_anomalies: Optional[bool] = None,
        status: Optional[str] = None,
        collection: Optional[str] = None,
        format: Optional[str] = None,
        search: Optional[str] = None
    ):
        self.job_id = job_id
        self.has_anomalies = has_anomalies
        self.status = status
        self.collection = collection
        self.format = format
        self.search = search


def get_book_filters(
    job_id: Optional[str] = None,
    has_anomalies: Optional[bool] = None,
    status: Optional[str] = None,
    collection: Optional[str] = None,
    format: Optional[str] = None,
    search: Optional[str] = None
) -> BookFilters:
    """Get book filtering parameters dependency."""
    return BookFilters(job_id, has_anomalies, status, collection, format, search)


class AnomalyFilters:
    """Anomaly filtering parameters."""

    def __init__(
        self,
        job_id: Optional[str] = None,
        severity: Optional[list] = None,
        anomaly_types: Optional[list] = None,
        resolved: Optional[bool] = None,
        collection: Optional[str] = None
    ):
        self.job_id = job_id
        self.severity = severity or []
        self.anomaly_types = anomaly_types or []
        self.resolved = resolved
        self.collection = collection


def get_anomaly_filters(
    job_id: Optional[str] = None,
    severity: Optional[str] = None,
    types: Optional[str] = None,
    resolved: Optional[bool] = None,
    collection: Optional[str] = None
) -> AnomalyFilters:
    """Get anomaly filtering parameters dependency."""
    # Parse comma-separated values
    severity_list = severity.split(',') if severity else []
    types_list = types.split(',') if types else []

    return AnomalyFilters(job_id, severity_list, types_list, resolved, collection)


class JobFilters:
    """Job filtering parameters."""

    def __init__(
        self,
        status: Optional[str] = None,
        root_directory: Optional[str] = None
    ):
        self.status = status
        self.root_directory = root_directory


def get_job_filters(
    status: Optional[str] = None,
    root_directory: Optional[str] = None
) -> JobFilters:
    """Get job filtering parameters dependency."""
    return JobFilters(status, root_directory)
