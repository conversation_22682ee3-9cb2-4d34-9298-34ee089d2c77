# FastAPI Backend for Directory Analyzer Dashboard

This FastAPI backend provides REST API endpoints for visualizing app state and triggering operations (including retry) for a React dashboard webapp.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
uv sync
```

### 2. Start the API Server
```bash
# Using the startup script
python start_api.py

# Or manually
uv run uvicorn api.main:app --reload --host 0.0.0.0 --port 8000
```

### 3. Access the API
- **Main API**: http://localhost:8000
- **Health Check**: http://localhost:8000/api/health
- **Interactive Docs**: http://localhost:8000/api/docs
- **ReDoc**: http://localhost:8000/api/redoc

## 📋 API Endpoints

### 🏠 Dashboard (App State Visualization)
- `GET /api/v1/dashboard/overview` - Complete dashboard overview
- `GET /api/v1/dashboard/summary` - Summary statistics
- `GET /api/v1/dashboard/recent-activity` - Recent job activity
- `GET /api/v1/dashboard/processing-stats` - Performance metrics
- `GET /api/v1/dashboard/system-status` - System health
- `GET /api/v1/dashboard/app-state` - **Complete app state (recommended for React)**

### 🔄 Operations (Including Retry)
- `POST /api/v1/operations/retry` - **Trigger retry operation**
- `POST /api/v1/operations/index` - Trigger indexing operation
- `GET /api/v1/operations/{operation_id}` - Get operation status
- `GET /api/v1/operations` - List recent operations
- `DELETE /api/v1/operations/{operation_id}` - Cancel operation

### 📋 Jobs
- `GET /api/v1/jobs` - List jobs with filtering
- `GET /api/v1/jobs/{job_id}` - Get job details
- `GET /api/v1/jobs/{job_id}/status` - **Real-time job status**
- `POST /api/v1/jobs` - Create job
- `DELETE /api/v1/jobs/{job_id}` - Cancel job

### 📚 Books
- `GET /api/v1/books` - List books with filtering
- `GET /api/v1/books/{book_id}` - Get book details
- `POST /api/v1/books/search` - Search books
- `GET /api/v1/books/collections/list` - List collections

### ⚠️ Anomalies
- `GET /api/v1/anomalies` - List anomalies with filtering
- `GET /api/v1/anomalies/{anomaly_id}` - Get anomaly details
- `PUT /api/v1/anomalies/{anomaly_id}/resolve` - Resolve anomaly
- `POST /api/v1/anomalies/resolve-bulk` - Bulk resolve anomalies

## 💡 React Dashboard Integration

### Key Endpoints for Dashboard

#### 1. Complete App State
```javascript
// Get all dashboard data in one call
const response = await fetch('/api/v1/dashboard/app-state');
const appState = await response.json();
```

#### 2. Trigger Retry Operation
```javascript
// Retry failed files
const response = await fetch('/api/v1/operations/retry', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ job_id: 'optional_job_id' })
});
const operation = await response.json();
```

#### 3. Monitor Operation Progress
```javascript
// Poll operation status
const response = await fetch(`/api/v1/operations/${operationId}`);
const status = await response.json();
```

#### 4. Real-time Job Status
```javascript
// Get job progress
const response = await fetch(`/api/v1/jobs/${jobId}/status`);
const jobStatus = await response.json();
```

### Example React Hook
```javascript
import { useState, useEffect } from 'react';

export function useAppState() {
  const [appState, setAppState] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAppState = async () => {
      try {
        const response = await fetch('/api/v1/dashboard/app-state');
        const data = await response.json();
        setAppState(data);
      } catch (error) {
        console.error('Failed to fetch app state:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAppState();
    const interval = setInterval(fetchAppState, 5000); // Poll every 5 seconds
    return () => clearInterval(interval);
  }, []);

  return { appState, loading };
}
```

## 🔧 Configuration

The API uses the same configuration as the main ebook indexer application:
- MongoDB connection settings
- Processing parameters
- Anomaly detection settings

## 🛠️ Development

### Testing the API
```bash
# Run the test script
python test_api.py
```

### API Documentation
Visit http://localhost:8000/api/docs for interactive API documentation with:
- Request/response schemas
- Try-it-out functionality
- Authentication details

## 📊 Response Formats

### App State Response
```json
{
  "system_health": {
    "status": "healthy",
    "database": { "connected": true },
    "configuration": { ... },
    "version": "1.0.0",
    "timestamp": "2024-01-01T12:00:00Z"
  },
  "dashboard_summary": {
    "total_books": 1500,
    "total_collections": 25,
    "total_anomalies": 12,
    "active_jobs": 1,
    "last_scan_date": "2024-01-01T10:00:00Z"
  },
  "active_operations": [],
  "recent_jobs": [...]
}
```

### Retry Operation Response
```json
{
  "operation_id": "uuid-string",
  "status": "pending",
  "estimated_files": 45,
  "message": "Retry operation started for 45 files"
}
```

## 🚨 Error Handling

All endpoints return consistent error responses:
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "field": "optional_field_name"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 🔒 CORS Configuration

The API is configured to accept requests from:
- `http://localhost:3000` (React dev server)
- `http://localhost:3001` (Alternative React dev server)

Update `api/main.py` to add additional origins as needed.

## 📝 Notes

- The API requires a MongoDB connection to function
- Operations are tracked in-memory (consider Redis for production)
- All timestamps are in UTC
- Pagination is supported on list endpoints
- Filtering is available on most list endpoints
