2025-05-26 00:02:38 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories - 345/345 items (100.0%) in 0:04:02.331955 (1.42 items/sec)
2025-05-26 00:02:54 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 68339285929e54069b3bccec completed: 364/364 files successful, 0 failed, 22 with anomalies
2025-05-26 00:04:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:04:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:05:07 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-26 00:06:11 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:06:11 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:09:19 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-26 00:09:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:09:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:09:48 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-26 00:09:48 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-26 00:09:48 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-26 00:09:48 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-26 00:10:28 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-26 00:10:28 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-26 00:10:28 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-26 00:10:28 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-26 00:15:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:15:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:15:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:15:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:15:57 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:15:57 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
