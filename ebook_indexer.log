2025-05-25 00:00:36 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:00:36 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:00:36 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-25 00:00:36 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-25 00:00:36 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-25 00:00:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-25 00:00:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 6832418411d05ca4cc376e83
2025-05-25 00:00:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 6832418411d05ca4cc376e83 for directories: ['test_ebooks']
2025-05-25 00:00:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 0 files to process
2025-05-25 00:00:36 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (0 items)
2025-05-25 00:00:36 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: test_ebooks
2025-05-25 00:00:36 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /home/<USER>/Projects-Python/Directory-Analyzer-Agent/test_ebooks
2025-05-25 00:00:36 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories
2025-05-25 00:00:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 0/0 (0.0%)
2025-05-25 00:00:36 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories - 1/0 items (0.0%) in 0:00:00.000075 (13333.33 items/sec)
2025-05-25 00:00:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 6832418411d05ca4cc376e83 completed: 1/1 files successful, 0 failed, 1 with anomalies
2025-05-25 00:01:23 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:01:23 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:01:23 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-25 00:01:23 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-25 00:01:23 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-25 00:01:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-25 00:01:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 683241b3e7a80e77ac1c19f4
2025-05-25 00:01:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 683241b3e7a80e77ac1c19f4 for directories: ['test_ebooks']
2025-05-25 00:01:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 1 files to process
2025-05-25 00:01:23 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (1 items)
2025-05-25 00:01:23 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: test_ebooks
2025-05-25 00:01:23 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /home/<USER>/Projects-Python/Directory-Analyzer-Agent/test_ebooks
2025-05-25 00:01:23 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories
2025-05-25 00:01:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 0/1 (0.0%)
2025-05-25 00:01:23 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories - 1/1 items (100.0%) in 0:00:00.000072 (13888.89 items/sec)
2025-05-25 00:01:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 683241b3e7a80e77ac1c19f4 completed: 1/1 files successful, 0 failed, 1 with anomalies
2025-05-25 00:01:44 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:01:44 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:01:55 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:01:55 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:03:00 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:03:00 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:03:43 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:03:43 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:03:54 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:03:54 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:04:13 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:04:13 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:05:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:05:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:05:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:05:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:06:46 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:06:46 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:08:25 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:08:25 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:10:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:10:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:10:29 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:10:29 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:10:29 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-25 00:10:29 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-25 00:10:29 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-25 00:10:29 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-25 00:10:29 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 683243d5481c310c6c8e8475
2025-05-25 00:10:29 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 683243d5481c310c6c8e8475 for directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202101']
2025-05-25 00:11:52 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 6080 files to process
2025-05-25 00:11:52 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (6080 items)
2025-05-25 00:11:52 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202101
2025-05-25 00:11:52 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202101
2025-05-25 00:11:59 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories
2025-05-25 00:11:59 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 0/6080 (0.0%)
2025-05-25 00:14:43 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 250/6080 (4.1%) - 1.52 items/sec - ETA: 1h 3m
2025-05-25 00:15:05 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 300/6080 (4.9%) - 1.62 items/sec - ETA: 59m 27s
2025-05-25 00:15:34 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 350/6080 (5.8%) - 1.63 items/sec - ETA: 58m 35s
2025-05-25 00:19:52 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:19:52 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:19:52 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-25 00:19:52 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-25 00:19:52 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-25 00:19:52 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-25 00:19:52 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 6832460861e85b92f29ea28c
2025-05-25 00:19:52 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 6832460861e85b92f29ea28c for directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202101']
2025-05-25 00:21:13 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 6080 files to process
2025-05-25 00:21:13 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (6080 items)
2025-05-25 00:21:13 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202101
2025-05-25 00:21:13 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202101
2025-05-25 00:21:20 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories
2025-05-25 00:21:20 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 0/6080 (0.0%)
2025-05-25 00:22:59 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 150/6080 (2.5%) - 1.51 items/sec - ETA: 1h 5m
2025-05-25 00:23:52 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 250/6080 (4.1%) - 1.64 items/sec - ETA: 59m 12s
2025-05-25 00:24:13 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 300/6080 (4.9%) - 1.73 items/sec - ETA: 55m 33s
2025-05-25 00:24:42 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 350/6080 (5.8%) - 1.73 items/sec - ETA: 55m 16s
2025-05-25 00:25:15 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 1250/6080 (20.6%) - 5.32 items/sec - ETA: 15m 8s
2025-05-25 00:26:35 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for contextual-design-2nd.pdf: Error reading PDF metadata: 'NullObject' object is not iterable
2025-05-25 00:27:28 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for advances-computers-121.pdf: Error reading PDF metadata: unhashable type: 'ArrayObject'
2025-05-25 00:27:59 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for days-before-ccnp-enterprise.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/xhtml/OEBPS/html/graphics/71hfd.jpg' in the archive"
2025-05-25 00:29:05 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for effective-programming-more-than-writing.epub: Error reading EPUB metadata: "There is no item named 'email.png' in the archive"
2025-05-25 00:29:06 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for comptia-cybersecurity-analyst-bundle-cs0-001.epub: Error reading EPUB metadata: 'tuple' object has no attribute 'href'
2025-05-25 00:29:22 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for Calculus Illustrated. Volume 3 Integral Calculus.pdf: Error reading PDF metadata: trailer can not be read ()
2025-05-25 00:29:23 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for Calculus Illustrated. Volume 4 Calculus in Higher Dimensions.pdf: Error reading PDF metadata: trailer can not be read ()
2025-05-25 00:30:30 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for Calculus Illustrated. Volume 1 Precalculus.pdf: Error reading PDF metadata: trailer can not be read ()
2025-05-25 00:32:08 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 2500/6080 (41.1%) - 3.86 items/sec - ETA: 15m 28s
2025-05-25 00:33:51 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 2700/6080 (44.4%) - 3.60 items/sec - ETA: 15m 40s
2025-05-25 00:34:15 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 2750/6080 (45.2%) - 3.55 items/sec - ETA: 15m 39s
2025-05-25 00:34:44 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 2800/6080 (46.1%) - 3.48 items/sec - ETA: 15m 42s
2025-05-25 00:35:07 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 2850/6080 (46.9%) - 3.44 items/sec - ETA: 15m 37s
2025-05-25 00:35:21 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for pragmatic-programmer-journey-mastery-2nd.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/xhtml/OEBPS/html/graphics/71hfd.jpg' in the archive"
2025-05-25 00:37:16 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 3100/6080 (51.0%) - 3.24 items/sec - ETA: 15m 19s
2025-05-25 00:37:29 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 3150/6080 (51.8%) - 3.25 items/sec - ETA: 15m 1s
2025-05-25 00:38:01 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for reasoning-probabilistic-deterministic-graphical-models.pdf: Error reading PDF metadata: Invalid Elementary Object starting with b'\xe9' @951136: b"bH\xd37\x89\xf8\xca\xae\xaa[\xbd|X['\xf5\riR\x19\xe97\x8c\xc9\xff\x7f\xf7\x1a\x82\x83\xfdBY\xe5\n\xa6\xb9\x84p\xda\xe8bs\xc0\x95\x16|\xcb\xd1\xbd\x83& \x9aF\x1f\x06\xc7\xc0\xb1\x99!9\xd9N\xc8Q\x9c\x87\x15\xd8\xf9\xa6Uh\xeb\xbc\x95\xad\xec"
2025-05-25 00:38:18 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 3250/6080 (53.5%) - 3.19 items/sec - ETA: 14m 46s
2025-05-25 00:39:14 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for microsoft-office-2019-inside-out.epub: Error reading EPUB metadata: list index out of range
2025-05-25 00:39:33 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for computer-organization-design-risc-v-2nd.pdf: Error reading PDF metadata: unhashable type: 'ArrayObject'
2025-05-25 00:39:50 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 3350/6080 (55.1%) - 3.02 items/sec - ETA: 15m 5s
2025-05-25 00:40:13 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 3400/6080 (55.9%) - 3.00 items/sec - ETA: 14m 53s
2025-05-25 00:40:48 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for introduction-embedded-systems-2nd.pdf: Error reading PDF metadata: PyCryptodome is required for AES algorithm
2025-05-25 00:40:55 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:40:55 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:40:55 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-25 00:40:55 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-25 00:40:55 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-25 00:40:55 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-25 00:40:55 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 68324af79421d83cd2407dc6
2025-05-25 00:40:55 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 68324af79421d83cd2407dc6 for directories: ['test_ebooks']
2025-05-25 00:40:55 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 1 files to process
2025-05-25 00:40:55 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (1 items)
2025-05-25 00:40:55 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: test_ebooks
2025-05-25 00:40:55 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /home/<USER>/Projects-Python/Directory-Analyzer-Agent/test_ebooks
2025-05-25 00:40:55 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories
2025-05-25 00:40:55 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 0/1 (0.0%)
2025-05-25 00:40:55 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories - 1/1 items (100.0%) in 0:00:00.000074 (13513.51 items/sec)
2025-05-25 00:40:55 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 68324af79421d83cd2407dc6 completed: 1/1 files successful, 0 failed, 1 with anomalies
2025-05-25 00:40:55 - ebook_indexer.database.repository.JobRepository - ERROR - MongoDB save_job failed: Job with ID 6832460861e85b92f29ea28c not found for update
2025-05-25 00:40:55 - ebook_indexer.core.indexer.EbookIndexer - ERROR - Failed to process file /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202101/react-native-essential-training/44 Test first.en.srt: MongoDB save_job failed: Job with ID 6832460861e85b92f29ea28c not found for update
2025-05-25 00:41:07 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for installing-configuring-windows-10-exam-70-698.epub: Error reading EPUB metadata: 'NoneType' object has no attribute 'get_name'
2025-05-25 00:41:13 - ebook_indexer.database.repository.JobRepository - ERROR - MongoDB save_job failed: Job with ID 6832460861e85b92f29ea28c not found for update
2025-05-25 00:41:13 - ebook_indexer.core.indexer.EbookIndexer - ERROR - Failed to process file /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202101/hands-functional-test-automation-selenium/hands-functional-test-automation-selenium.pdf: MongoDB save_job failed: Job with ID 6832460861e85b92f29ea28c not found for update
2025-05-25 00:41:56 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for advances-computers-120.pdf: Error reading PDF metadata: unhashable type: 'ArrayObject'
2025-05-25 00:42:01 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 3700/6080 (60.9%) - 2.98 items/sec - ETA: 13m 18s
2025-05-25 00:42:02 - ebook_indexer.database.repository.JobRepository - ERROR - MongoDB save_job failed: Job with ID 6832460861e85b92f29ea28c not found for update
2025-05-25 00:42:02 - ebook_indexer.core.indexer.EbookIndexer - ERROR - Failed to process file /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202101/real-world-bug-hunting_60128162/real-world-bug-hunting.mobi: MongoDB save_job failed: Job with ID 6832460861e85b92f29ea28c not found for update
2025-05-25 00:42:02 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:42:02 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:42:02 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-25 00:42:02 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-25 00:42:02 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-25 00:42:02 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-25 00:42:02 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 68324b3a9f5b5e47bbb5e7db
2025-05-25 00:42:02 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 68324b3a9f5b5e47bbb5e7db for directories: ['test_ebooks']
2025-05-25 00:42:02 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 1 files to process
2025-05-25 00:42:02 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (1 items)
2025-05-25 00:42:02 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: test_ebooks
2025-05-25 00:42:02 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /home/<USER>/Projects-Python/Directory-Analyzer-Agent/test_ebooks
2025-05-25 00:42:02 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories
2025-05-25 00:42:02 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 0/1 (0.0%)
2025-05-25 00:42:02 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories - 1/1 items (100.0%) in 0:00:00.000075 (13333.33 items/sec)
2025-05-25 00:42:02 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 68324b3a9f5b5e47bbb5e7db completed: 1/1 files successful, 0 failed, 1 with anomalies
2025-05-25 00:42:46 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 3800/6080 (62.5%) - 2.95 items/sec - ETA: 12m 51s
2025-05-25 00:42:48 - ebook_indexer.database.repository.JobRepository - ERROR - MongoDB save_job failed: Job with ID 6832460861e85b92f29ea28c not found for update
2025-05-25 00:42:48 - ebook_indexer.core.indexer.EbookIndexer - ERROR - Failed to process file /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202101/servers-for-hackers/servers-for-hackers.mobi: MongoDB save_job failed: Job with ID 6832460861e85b92f29ea28c not found for update
2025-05-25 00:43:41 - ebook_indexer.database.repository.JobRepository - ERROR - MongoDB save_job failed: Job with ID 6832460861e85b92f29ea28c not found for update
2025-05-25 00:43:41 - ebook_indexer.core.indexer.EbookIndexer - ERROR - Failed to process file /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202101/windows-terminal-productivity-hacks/windows-terminal-productivity-hacks.mobi: MongoDB save_job failed: Job with ID 6832460861e85b92f29ea28c not found for update
2025-05-25 00:44:44 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:44:44 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:44:44 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-25 00:44:44 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-25 00:44:44 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-25 00:44:44 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-25 00:44:44 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 68324bdcd385b20a33ce5bd9
2025-05-25 00:44:44 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 68324bdcd385b20a33ce5bd9 for directories: ['test_ebooks']
2025-05-25 00:44:44 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 3 files to process
2025-05-25 00:44:44 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (3 items)
2025-05-25 00:44:44 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: test_ebooks
2025-05-25 00:44:44 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /home/<USER>/Projects-Python/Directory-Analyzer-Agent/test_ebooks
2025-05-25 00:44:44 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories
2025-05-25 00:44:44 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 0/3 (0.0%)
2025-05-25 00:44:44 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories - 3/3 items (100.0%) in 0:00:00.001188 (2525.25 items/sec)
2025-05-25 00:44:44 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 68324bdcd385b20a33ce5bd9 completed: 3/3 files successful, 0 failed, 2 with anomalies
2025-05-25 00:45:52 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:45:52 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:46:10 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:46:10 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:47:08 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:47:08 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:47:08 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-25 00:47:08 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-25 00:47:08 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-25 00:47:08 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-25 00:47:08 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 68324c6c00f4c8aa509d15fc
2025-05-25 00:47:08 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 68324c6c00f4c8aa509d15fc for directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202101']
2025-05-25 00:49:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:49:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:49:20 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-25 00:49:20 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-25 00:49:20 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-25 00:49:20 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-25 00:49:20 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 68324cf03563da14e3801f81
2025-05-25 00:49:20 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 68324cf03563da14e3801f81 for directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402']
2025-05-25 00:49:25 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 345 files to process
2025-05-25 00:49:25 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (345 items)
2025-05-25 00:49:25 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402
2025-05-25 00:49:25 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402
2025-05-25 00:49:26 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories
2025-05-25 00:49:26 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 0/345 (0.0%)
2025-05-25 00:49:54 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 50/345 (14.5%) - 1.78 items/sec - ETA: 2m 46s
2025-05-25 00:50:41 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 100/345 (29.0%) - 1.33 items/sec - ETA: 3m 4s
2025-05-25 00:50:43 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for ruby-rails-tutorial-7th.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/graphics/f0550-02.jpg' in the archive"
2025-05-25 00:53:27 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories - 345/345 items (100.0%) in 0:04:01.311357 (1.43 items/sec)
2025-05-25 00:53:43 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 68324cf03563da14e3801f81 completed: 364/364 files successful, 0 failed, 22 with anomalies
2025-05-25 00:54:17 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:54:17 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:54:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:54:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 23:44:44 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:44:49 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338f4ccdf344da11beafbb, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:44:49 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338f4ccdf344da11beafbb, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:45:08 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:45:13 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338f6490e59438e5bbc896, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:45:13 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338f6490e59438e5bbc896, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:45:34 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:45:39 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338f7e11658f6592d3829c, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:45:39 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338f7e11658f6592d3829c, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:45:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-25 23:45:45 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:45:50 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338f893ef1a12de95673b7, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:45:50 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338f893ef1a12de95673b7, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:45:57 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:46:02 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338f953142a8723817826b, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:46:02 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338f953142a8723817826b, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:46:10 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:46:15 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338fa28e7cbe3ba35911f3, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:46:15 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338fa28e7cbe3ba35911f3, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:46:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:46:25 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338fac1b2b146158d4e32e, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:46:25 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338fac1b2b146158d4e32e, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:46:48 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:46:53 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338fc8eaf8e177331cdcd2, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:46:53 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68338fc8eaf8e177331cdcd2, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:51:33 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:51:38 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683390e5eaf8e177331cdcd3, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:51:38 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683390e5eaf8e177331cdcd3, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:53:55 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:53:55 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 23:54:42 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:54:47 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683391a23fa6d3f5aa8ddaec, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:54:47 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683391a23fa6d3f5aa8ddaec, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-25 23:58:05 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:58:05 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 23:58:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:58:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 23:58:29 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 23:58:29 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 23:58:29 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-25 23:58:29 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-25 23:58:29 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-25 23:58:29 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-25 23:58:29 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 68339285929e54069b3bccec
2025-05-25 23:58:29 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 68339285929e54069b3bccec for directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402']
2025-05-25 23:58:35 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 345 files to process
2025-05-25 23:58:35 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (345 items)
2025-05-25 23:58:35 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402
2025-05-25 23:58:35 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402
2025-05-25 23:58:35 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories
2025-05-25 23:58:35 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 0/345 (0.0%)
2025-05-25 23:59:03 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 50/345 (14.5%) - 1.80 items/sec - ETA: 2m 43s
2025-05-25 23:59:53 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for ruby-rails-tutorial-7th.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/graphics/f0550-02.jpg' in the archive"
