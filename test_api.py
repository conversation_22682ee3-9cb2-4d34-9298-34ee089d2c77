#!/usr/bin/env python3
"""Test script for the FastAPI backend."""

import sys
import asyncio
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

async def test_api_startup():
    """Test that the API can start up without errors."""
    try:
        # Import the FastAPI app
        from api.main import app
        
        print("✅ FastAPI app imported successfully")
        
        # Test that routers are properly imported
        from api.routers import dashboard, jobs, operations, books, anomalies
        print("✅ All routers imported successfully")
        
        # Test middleware import
        from api.middleware import LoggingMiddleware
        print("✅ Middleware imported successfully")
        
        # Test dependencies
        from api.dependencies import get_database_connection, get_app_config
        print("✅ Dependencies imported successfully")
        
        # Test models
        from api.models.responses import DashboardOverview, AppState
        from api.models.requests import RetryOperationRequest
        print("✅ Models imported successfully")
        
        print("\n🎉 All API components loaded successfully!")
        print("\nTo start the API server, run:")
        print("uvicorn api.main:app --reload --host 0.0.0.0 --port 8000")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading API components: {e}")
        import traceback
        traceback.print_exc()
        return False

def print_api_endpoints():
    """Print available API endpoints."""
    print("\n📋 Available API Endpoints:")
    print("\n🏠 Dashboard (App State Visualization):")
    print("  GET  /api/v1/dashboard/overview          - Complete dashboard overview")
    print("  GET  /api/v1/dashboard/summary           - Summary statistics")
    print("  GET  /api/v1/dashboard/recent-activity   - Recent job activity")
    print("  GET  /api/v1/dashboard/processing-stats  - Performance metrics")
    print("  GET  /api/v1/dashboard/system-status     - System health")
    print("  GET  /api/v1/dashboard/app-state         - Complete app state")
    
    print("\n🔄 Operations (Including Retry):")
    print("  POST /api/v1/operations/retry            - Trigger retry operation")
    print("  POST /api/v1/operations/index            - Trigger indexing operation")
    print("  GET  /api/v1/operations/{operation_id}   - Get operation status")
    print("  GET  /api/v1/operations                  - List recent operations")
    print("  DEL  /api/v1/operations/{operation_id}   - Cancel operation")
    
    print("\n📋 Jobs:")
    print("  GET  /api/v1/jobs                        - List jobs")
    print("  GET  /api/v1/jobs/{job_id}               - Get job details")
    print("  GET  /api/v1/jobs/{job_id}/status        - Get job status")
    print("  POST /api/v1/jobs                        - Create job")
    print("  DEL  /api/v1/jobs/{job_id}               - Cancel job")
    
    print("\n📚 Books:")
    print("  GET  /api/v1/books                       - List books")
    print("  GET  /api/v1/books/{book_id}             - Get book details")
    print("  POST /api/v1/books/search                - Search books")
    print("  GET  /api/v1/books/collections/list      - List collections")
    
    print("\n⚠️  Anomalies:")
    print("  GET  /api/v1/anomalies                   - List anomalies")
    print("  GET  /api/v1/anomalies/{anomaly_id}      - Get anomaly details")
    print("  PUT  /api/v1/anomalies/{anomaly_id}/resolve - Resolve anomaly")
    print("  POST /api/v1/anomalies/resolve-bulk      - Bulk resolve anomalies")
    
    print("\n🔧 System:")
    print("  GET  /                                   - Root endpoint")
    print("  GET  /api/health                         - Health check")
    print("  GET  /api/docs                           - API documentation")

def print_usage_examples():
    """Print usage examples for React dashboard."""
    print("\n💡 Usage Examples for React Dashboard:")
    
    print("\n1. Get complete app state (recommended for dashboard):")
    print("   GET /api/v1/dashboard/app-state")
    
    print("\n2. Trigger retry operation:")
    print("   POST /api/v1/operations/retry")
    print("   Body: {\"job_id\": \"optional_job_id\"}")
    
    print("\n3. Monitor operation progress:")
    print("   GET /api/v1/operations/{operation_id}")
    
    print("\n4. Get real-time job status:")
    print("   GET /api/v1/jobs/{job_id}/status")
    
    print("\n5. Search books:")
    print("   POST /api/v1/books/search")
    print("   Body: {\"query\": \"python programming\", \"fields\": [\"title\", \"author\"]}")

if __name__ == "__main__":
    print("🚀 Testing FastAPI Backend for Directory Analyzer")
    print("=" * 50)
    
    # Test API startup
    success = asyncio.run(test_api_startup())
    
    if success:
        print_api_endpoints()
        print_usage_examples()
        
        print("\n🎯 Next Steps:")
        print("1. Start the API server: uvicorn api.main:app --reload --port 8000")
        print("2. Visit http://localhost:8000/api/docs for interactive API documentation")
        print("3. Test endpoints with your React dashboard")
        print("4. Use /api/v1/dashboard/app-state for complete dashboard state")
        print("5. Use /api/v1/operations/retry to trigger retry operations")
    else:
        print("\n❌ API startup test failed. Please fix the errors above.")
        sys.exit(1)
