#!/usr/bin/env python3
"""Start the FastAPI server for the Directory Analyzer Dashboard."""

import sys
import subprocess
from pathlib import Path

def main():
    """Start the FastAPI server."""
    print("🚀 Starting Directory Analyzer FastAPI Backend")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("api/main.py").exists():
        print("❌ Error: api/main.py not found. Please run from project root.")
        sys.exit(1)
    
    print("📋 API will be available at:")
    print("  • Main API: http://localhost:8000")
    print("  • Health Check: http://localhost:8000/api/health")
    print("  • API Docs: http://localhost:8000/api/docs")
    print("  • Interactive Docs: http://localhost:8000/api/redoc")
    print()
    
    print("🎯 Key Endpoints for React Dashboard:")
    print("  • App State: GET /api/v1/dashboard/app-state")
    print("  • Retry Operation: POST /api/v1/operations/retry")
    print("  • Job Status: GET /api/v1/jobs/{job_id}/status")
    print()
    
    print("🔄 Starting server...")
    print("Press Ctrl+C to stop")
    print("-" * 50)
    
    try:
        # Start the server using uv run
        subprocess.run([
            "uv", "run", "uvicorn", 
            "api.main:app",
            "--reload",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--log-level", "info"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Server failed to start: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print("\n❌ Error: 'uv' command not found. Please install uv or run manually:")
        print("uvicorn api.main:app --reload --host 0.0.0.0 --port 8000")
        sys.exit(1)

if __name__ == "__main__":
    main()
